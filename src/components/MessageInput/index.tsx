import React, { useState, useEffect } from 'react';
import { TextInput, StyleSheet, TouchableOpacity, Text, Linking, Alert, Platform, View, ScrollView } from 'react-native';
import SavedMessagesModal from '../SavedMessagesModal';
import SaveMessageModal from '../SaveMessageModal';
import { SavedMessage } from '../../types/savedMessage';
import { SavedMessageService } from '../../services/savedMessageService';

interface MessageInputProps {
  onChange: (message: string) => void;
  phoneNumber: string;
}

const MessageInput: React.FC<MessageInputProps> = ({ onChange, phoneNumber }) => {
  const [message, setMessage] = useState('');
  const [savedMessagesModalVisible, setSavedMessagesModalVisible] = useState(false);
  const [saveMessageModalVisible, setSaveMessageModalVisible] = useState(false);
  const [editingMessage, setEditingMessage] = useState<SavedMessage | null>(null);
  const [savedMessages, setSavedMessages] = useState<SavedMessage[]>([]);
  const [showAllChips, setShowAllChips] = useState(false);

  useEffect(() => {
    initializeAndLoadMessages();
  }, []);

  const initializeAndLoadMessages = async () => {
    try {
      // Initialize default messages if none exist
      await SavedMessageService.initializeDefaultMessages();
      // Then load all messages
      await loadSavedMessages();
    } catch (error) {
      console.error('Error initializing messages:', error);
    }
  };

  const loadSavedMessages = async () => {
    try {
      const messages = await SavedMessageService.getAllMessages();
      setSavedMessages(messages);
    } catch (error) {
      console.error('Error loading saved messages:', error);
    }
  };

  const handleSend = async () => {
    try {
      const whatsappUrl = `whatsapp://send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;
      Linking.openURL(whatsappUrl).catch(() => Alert.alert('Unable to open WhatsApp'));
    } catch (error) {
      console.error('Error sharing to WhatsApp:', error);
    }
  };

  const handleSaveMessage = () => {
    if (!message.trim()) {
      Alert.alert('Error', 'Please enter a message to save');
      return;
    }
    setEditingMessage(null);
    setSaveMessageModalVisible(true);
  };

  const handleLoadMessages = () => {
    setSavedMessagesModalVisible(true);
  };

  const handleSelectMessage = (savedMessage: SavedMessage) => {
    setMessage(savedMessage.content);
    onChange(savedMessage.content);
  };

  const handleEditMessage = (savedMessage: SavedMessage) => {
    setEditingMessage(savedMessage);
    setSaveMessageModalVisible(true);
  };

  const handleChipSelect = (savedMessage: SavedMessage) => {
    setMessage(savedMessage.content);
    onChange(savedMessage.content);
  };

  const handleSaveSuccess = () => {
    loadSavedMessages(); // Refresh the chips when a message is saved
  };

  const displayedMessages = showAllChips ? savedMessages : savedMessages.slice(0, 3);
  const hasMoreMessages = savedMessages.length > 3;

  return (
    <View style={styles.container}>
      {/* Saved Messages Chips */}
      {savedMessages.length > 0 && (
        <View style={styles.chipsSection}>
          <Text style={styles.chipsLabel}>Quick Messages:</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.chipsScrollView}
            contentContainerStyle={styles.chipsContainer}
          >
            {displayedMessages.map((savedMessage) => (
              <TouchableOpacity
                key={savedMessage.id}
                style={styles.messageChip}
                onPress={() => handleChipSelect(savedMessage)}
                activeOpacity={0.7}
              >
                <Text style={styles.chipText} numberOfLines={1}>
                  {savedMessage.title}
                </Text>
              </TouchableOpacity>
            ))}
            {hasMoreMessages && !showAllChips && (
              <TouchableOpacity
                style={styles.moreChip}
                onPress={() => setShowAllChips(true)}
                activeOpacity={0.7}
              >
                <Text style={styles.moreChipText}>+{savedMessages.length - 3}</Text>
              </TouchableOpacity>
            )}
            {showAllChips && hasMoreMessages && (
              <TouchableOpacity
                style={styles.moreChip}
                onPress={() => setShowAllChips(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.moreChipText}>Show Less</Text>
              </TouchableOpacity>
            )}
          </ScrollView>
        </View>
      )}

      <TextInput
        style={styles.input}
        placeholder="Type your message..."
        placeholderTextColor="#718096"
        onChangeText={(text) => {
          setMessage(text);
          onChange(text);
        }}
        value={message}
        multiline
        textAlignVertical="top"
      />

      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[
            styles.iconButton,
            !message.trim() && styles.iconButtonDisabled
          ]}
          onPress={handleSaveMessage}
          activeOpacity={0.8}
          disabled={!message.trim()}
        >
          <Text style={[
            styles.iconButtonText,
            !message.trim() && styles.iconButtonTextDisabled
          ]}>💾</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.iconButton}
          onPress={handleLoadMessages}
          activeOpacity={0.8}
        >
          <Text style={styles.iconButtonText}>📋</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.mainButton}
          onPress={handleSend}
          activeOpacity={0.8}
        >
          <Text style={styles.buttonText}>Open in WhatsApp</Text>
        </TouchableOpacity>
      </View>

      <SavedMessagesModal
        visible={savedMessagesModalVisible}
        onClose={() => {
          setSavedMessagesModalVisible(false);
          loadSavedMessages(); // Refresh chips when modal closes
        }}
        onSelectMessage={handleSelectMessage}
        onEditMessage={handleEditMessage}
      />

      <SaveMessageModal
        visible={saveMessageModalVisible}
        onClose={() => setSaveMessageModalVisible(false)}
        messageContent={message}
        editingMessage={editingMessage}
        onSaveSuccess={handleSaveSuccess}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  input: {
    minHeight: 120,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    fontSize: 16,
    color: '#2D3748',
    backgroundColor: '#ffffff',
    ...Platform.select({
      ios: {
        shadowColor: '#2D3748',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  iconButton: {
    width: 48,
    height: 56,
    borderRadius: 12,
    backgroundColor: '#F7FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#2D3748',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  iconButtonText: {
    fontSize: 18,
  },
  iconButtonDisabled: {
    backgroundColor: '#F1F5F9',
    borderColor: '#CBD5E0',
  },
  iconButtonTextDisabled: {
    opacity: 0.5,
  },
  mainButton: {
    flex: 1,
    backgroundColor: '#25D366',
    paddingHorizontal: 12,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    height: 56,
    minWidth: 140, // Ensure minimum width for the text
    ...Platform.select({
      ios: {
        shadowColor: '#1A8D3D',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  buttonText: {
    color: 'white',
    fontSize: 15,
    fontWeight: '600',
    letterSpacing: 0.3,
    textAlign: 'center',
  },
  chipsSection: {
    marginTop: 4,
    marginBottom: 16,
    paddingTop: 4,
  },
  chipsLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#718096',
    marginBottom: 10,
    marginTop: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    lineHeight: 16,
  },
  chipsScrollView: {
    flexGrow: 0,
  },
  chipsContainer: {
    paddingRight: 16,
  },
  messageChip: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    maxWidth: 120,
    ...Platform.select({
      ios: {
        shadowColor: '#2D3748',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
    }),
  },
  chipText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#2D3748',
    textAlign: 'center',
  },
  moreChip: {
    backgroundColor: '#F7FAFC',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderStyle: 'dashed',
  },
  moreChipText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#718096',
    textAlign: 'center',
  },
});

export default MessageInput;